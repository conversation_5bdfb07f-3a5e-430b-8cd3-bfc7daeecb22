// Copyright 2024. Quantower Delta Strategy.
// 基于200 tick周期的Delta交易策略
// 做多：连续两根K线收盘价>开盘价，且Delta值都>10
// 做空：连续两根K线收盘价<开盘价，且Delta值都<-10
// 止盈：3个价格点数，止损：2个价格点数

using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using TradingPlatform.BusinessLayer;

namespace QuantowerDeltaStrategy
{
    /// <summary>
    /// 基于Delta指标的200 tick周期交易策略
    /// Delta = 主动买单量 - 主动卖单量
    /// </summary>
    public sealed class DeltaTickStrategy : Strategy, ICurrentAccount, ICurrentSymbol
    {
        #region 输入参数

        /// <summary>
        /// 交易品种
        /// </summary>
        [InputParameter("Symbol", 0)]
        public Symbol CurrentSymbol { get; set; }

        /// <summary>
        /// 交易账户
        /// </summary>
        [InputParameter("Account", 1)]
        public Account CurrentAccount { get; set; }

        /// <summary>
        /// 每次交易数量
        /// </summary>
        [InputParameter("Quantity", 2, 0.1, 99999, 0.1, 2)]
        public double Quantity { get; set; } = 1.0;

        /// <summary>
        /// 200 tick聚合周期
        /// </summary>
        [InputParameter("Tick Count", 3, 50, 1000, 50, 0)]
        public int TickCount { get; set; } = 200;

        /// <summary>
        /// Delta阈值（做多/做空的Delta临界值）
        /// </summary>
        [InputParameter("Delta Threshold", 4, 0, 500, 10, 0)]
        public int DeltaThreshold { get; set; } = 10;

        /// <summary>
        /// 止盈点数（直接使用价格点数）
        /// 例如：输入3点，实际价格差值=3.0
        /// </summary>
        [InputParameter("Take Profit (Points)", 5, 0.1, 100.0, 0.1, 1)]
        public double TakeProfit { get; set; } = 3.0;

        /// <summary>
        /// 止损点数（直接使用价格点数）
        /// 例如：输入2点，实际价格差值=2.0
        /// </summary>
        [InputParameter("Stop Loss (Points)", 6, 0.1, 100.0, 0.1, 1)]
        public double StopLoss { get; set; } = 2.0;

        #endregion

        #region 常量定义

        /// <summary>
        /// MNQ合约tick大小（每tick=0.25点）
        /// 注意：这里直接使用点数，不进行tick转换
        /// </summary>
        private const double MNQ_TICK_SIZE = 1.0;

        #endregion

        #region 私有变量

        /// <summary>
        /// 监控连接ID
        /// </summary>
        public override string[] MonitoringConnectionsIds => new string[] {
            this.CurrentSymbol?.ConnectionId,
            this.CurrentAccount?.ConnectionId
        };

        /// <summary>
        /// 200 tick历史数据
        /// </summary>
        private HistoricalData tickHistoricalData;

        /// <summary>
        /// 订单类型ID（市价单）
        /// </summary>
        private string orderTypeId;

        /// <summary>
        /// 等待开仓标志
        /// </summary>
        private bool waitOpenPosition;

        /// <summary>
        /// 等待平仓标志
        /// </summary>
        private bool waitClosePositions;

        /// <summary>
        /// 当前K线是否已经检查过交易信号
        /// </summary>
        private bool currentBarSignalChecked;

        /// <summary>
        /// 当前持仓的止盈订单ID
        /// </summary>
        private string currentTakeProfitOrderId;

        /// <summary>
        /// 当前持仓的止损订单ID
        /// </summary>
        private string currentStopLossOrderId;

        /// <summary>
        /// 存储已完成的K线数据（用于分析连续两根K线）
        /// </summary>
        private List<TickBarData> completedBars = [];

        /// <summary>
        /// 存储最近几个tick的价格，用于更精确的Delta计算
        /// </summary>
        private List<double> recentPrices = [];

        /// <summary>
        /// 当前tick计数器
        /// </summary>
        private int currentTickCount;

        /// <summary>
        /// 当前Delta累计值
        /// </summary>
        private double currentDelta;

        /// <summary>
        /// 当前K线开盘价
        /// </summary>
        private double currentOpen;

        /// <summary>
        /// 当前K线收盘价
        /// </summary>
        private double currentClose;

        /// <summary>
        /// 总盈亏统计
        /// </summary>
        private double totalNetPl;
        private double totalGrossPl;
        private double totalFee;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeltaTickStrategy() : base()
        {
            this.Name = "Delta Tick Strategy";
            this.Description = "基于200 tick周期Delta指标的交易策略";

            // .NET 8.0: 变量已在声明时初始化，无需重复初始化
            this.currentTickCount = 0;
            this.currentDelta = 0;
            this.totalNetPl = 0D;
        }

        #endregion

        #region 数据结构

        /// <summary>
        /// Tick K线数据结构
        /// </summary>
        private class TickBarData
        {
            public double Open { get; set; }
            public double Close { get; set; }
            public double High { get; set; }
            public double Low { get; set; }
            public double Delta { get; set; }
            public DateTime TimeLeft { get; set; }
            public int TickCount { get; set; }

            /// <summary>
            /// 判断是否为阳线（收盘价>开盘价）
            /// </summary>
            public bool IsBullish => Close > Open;

            /// <summary>
            /// 判断是否为阴线（收盘价<开盘价）
            /// </summary>
            public bool IsBearish => Close < Open;
        }

        #endregion

        #region 策略生命周期方法

        /// <summary>
        /// 策略启动时调用
        /// </summary>
        protected override void OnRun()
        {
            this.totalNetPl = 0D;

#if DEBUG
            // 调试模式下的详细日志
            this.Log("[DEBUG] 策略开始启动 - 进入OnRun方法", StrategyLoggingLevel.Info);
            System.Diagnostics.Debugger.Break(); // 调试断点
#endif

            // 验证Symbol
            if (this.CurrentSymbol != null && this.CurrentSymbol.State == BusinessObjectState.Fake)
                this.CurrentSymbol = Core.Instance.GetSymbol(this.CurrentSymbol.CreateInfo());

            if (this.CurrentSymbol == null)
            {
                this.Log("错误：未指定交易品种", StrategyLoggingLevel.Error);
                return;
            }

            // 验证Account
            if (this.CurrentAccount != null && this.CurrentAccount.State == BusinessObjectState.Fake)
                this.CurrentAccount = Core.Instance.GetAccount(this.CurrentAccount.CreateInfo());

            if (this.CurrentAccount == null)
            {
                this.Log("错误：未指定交易账户", StrategyLoggingLevel.Error);
                return;
            }

            // 验证Symbol和Account来自同一连接
            if (this.CurrentSymbol.ConnectionId != this.CurrentAccount.ConnectionId)
            {
                this.Log("错误：交易品种和账户来自不同的连接", StrategyLoggingLevel.Error);
                return;
            }

            // 获取市价单订单类型
            this.orderTypeId = Core.OrderTypes.FirstOrDefault(x => 
                x.ConnectionId == this.CurrentSymbol.ConnectionId && 
                x.Behavior == OrderTypeBehavior.Market)?.Id;

            if (string.IsNullOrEmpty(this.orderTypeId))
            {
                this.Log("错误：连接不支持市价单", StrategyLoggingLevel.Error);
                return;
            }

            try
            {
                // 订阅实时tick数据
                this.CurrentSymbol.NewLast += this.Symbol_NewLast;

                // 订阅交易事件
                Core.PositionAdded += this.Core_PositionAdded;
                Core.PositionRemoved += this.Core_PositionRemoved;
                Core.OrdersHistoryAdded += this.Core_OrdersHistoryAdded;
                Core.TradeAdded += this.Core_TradeAdded;

                this.Log($"策略启动成功 - 品种: {this.CurrentSymbol.Name}, 账户: {this.CurrentAccount.Name}", StrategyLoggingLevel.Info);
                this.Log($"参数设置 - Tick周期: {this.TickCount}, Delta阈值: ±{this.DeltaThreshold}, 止盈: {this.TakeProfit:F1}, 止损: {this.StopLoss:F1}", StrategyLoggingLevel.Info);
            }
            catch (Exception ex)
            {
                this.Log($"策略启动失败: {ex.Message}", StrategyLoggingLevel.Error);
                this.Stop();
            }
        }

        /// <summary>
        /// 策略停止时调用
        /// </summary>
        protected override void OnStop()
        {
            try
            {
                // 取消订阅事件
                if (this.CurrentSymbol != null)
                    this.CurrentSymbol.NewLast -= this.Symbol_NewLast;

                Core.PositionAdded -= this.Core_PositionAdded;
                Core.PositionRemoved -= this.Core_PositionRemoved;
                Core.OrdersHistoryAdded -= this.Core_OrdersHistoryAdded;
                Core.TradeAdded -= this.Core_TradeAdded;

                this.Log("策略已停止", StrategyLoggingLevel.Info);
            }
            catch (Exception ex)
            {
                this.Log($"策略停止时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }

            base.OnStop();
        }

        #endregion

        #region Tick数据处理

        /// <summary>
        /// 处理新的Last tick数据
        /// </summary>
        private void Symbol_NewLast(Symbol symbol, Last last)
        {
            try
            {
                // 保存前一个价格（修复关键bug）
                double previousPrice = this.currentClose;

                // 如果是第一个tick，初始化开盘价
                if (this.currentTickCount == 0)
                {
                    this.currentOpen = last.Price;
                    this.currentClose = last.Price;
                    previousPrice = last.Price; // 第一个tick没有前价格
                    this.Log($"初始化K线 - 开盘价: {this.currentOpen:F4}", StrategyLoggingLevel.Info);
                }
                else
                {
                    // 更新当前收盘价
                    this.currentClose = last.Price;
                }

                this.currentTickCount++;

                // 记录价格历史（保留最近20个价格）
                this.recentPrices.Add(last.Price);
                if (this.recentPrices.Count > 20)
                {
                    this.recentPrices.RemoveAt(0);
                }

                // 计算Delta（改进后的版本）
                double deltaContribution = this.CalculateDeltaFromTickFixed(last, previousPrice);
                this.currentDelta += deltaContribution;

                // 每50个tick输出一次状态
                if (this.currentTickCount % 50 == 0)
                {
                    this.Log($"[STATUS] Tick进度: {this.currentTickCount}/{this.TickCount}, 当前Delta: {this.currentDelta:F2}, 价格: {last.Price:F4}",
                        StrategyLoggingLevel.Info);
                }

                // 当达到指定tick数量时，完成一根K线
                if (this.currentTickCount >= this.TickCount)
                {
                    this.CompleteCurrentBar();
                }
            }
            catch (Exception ex)
            {
                this.Log($"处理tick数据时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 改进的Delta计算方法 - 使用真实权重
        /// </summary>
        private double CalculateDeltaFromTickFixed(Last last, double previousPrice)
        {
            double priceChange = last.Price - previousPrice;

            // 使用真实成交量作为权重，如果没有则使用价格变化幅度作为权重
            double volume = last.Size > 0 ? last.Size : Math.Abs(priceChange) * 100;

            // 只在每50个tick输出一次详细调试信息
            bool shouldLog = this.currentTickCount % 50 == 0;

            if (shouldLog)
            {
                this.Log($"[DEBUG] Tick#{this.currentTickCount}: 价格={last.Price:F4}, 前价格={previousPrice:F4}, 变化={priceChange:F4}, 成交量={volume:F2}",
                    StrategyLoggingLevel.Info);
            }

            double deltaContribution = 0;

            if (Math.Abs(priceChange) > 0.1) // 有价格变化（MNQ最小变动0.25）
            {
                if (priceChange > 0)
                    deltaContribution = volume; // 正Delta（买入压力）
                else
                    deltaContribution = -volume; // 负Delta（卖出压力）

                if (shouldLog)
                {
                    this.Log($"[DEBUG] 检测到价格变化! 变化={priceChange:F4}, Delta贡献={deltaContribution:F2}",
                        StrategyLoggingLevel.Info);
                }
            }
            else // 价格无变化
            {
                deltaContribution = 0;
                if (shouldLog)
                {
                    this.Log($"[DEBUG] 价格无变化, Delta贡献=0", StrategyLoggingLevel.Info);
                }
            }

            if (shouldLog)
            {
                this.Log($"[DEBUG] Delta贡献值: {deltaContribution:F2}, 累计Delta: {this.currentDelta + deltaContribution:F2}",
                    StrategyLoggingLevel.Info);
            }

            return deltaContribution;
        }

        /// <summary>
        /// 原有的Delta计算方法（保留作为备用）
        /// </summary>
        private double CalculateDeltaFromTick(Last last)
        {
            // 这个方法有bug，已被CalculateDeltaFromTickFixed替代
            double previousPrice = this.currentTickCount == 1 ? this.currentOpen : this.currentClose;
            double priceChange = last.Price - previousPrice;
            return priceChange > 0 ? 5 : (priceChange < 0 ? -5 : 0);
        }

        /// <summary>
        /// 完成当前K线并检查交易信号
        /// </summary>
        private void CompleteCurrentBar()
        {
            try
            {
                // 创建完成的K线数据
                var completedBar = new TickBarData
                {
                    Open = this.currentOpen,
                    Close = this.currentClose,
                    High = Math.Max(this.currentOpen, this.currentClose), // 简化：使用开盘和收盘价
                    Low = Math.Min(this.currentOpen, this.currentClose),   // 简化：使用开盘和收盘价
                    Delta = this.currentDelta,
                    TimeLeft = Core.Instance.TimeUtils.DateTimeUtcNow,
                    TickCount = this.currentTickCount
                };

                // 添加到已完成K线列表
                this.completedBars.Add(completedBar);

                // 保持最近的10根K线（用于分析）
                if (this.completedBars.Count > 10)
                    this.completedBars.RemoveAt(0);

                this.Log($"完成K线 - 开盘: {completedBar.Open:F4}, 收盘: {completedBar.Close:F4}, Delta: {completedBar.Delta:F2}, Tick数: {completedBar.TickCount}",
                    StrategyLoggingLevel.Info);

                // 检查交易信号
                this.CheckTradingSignals();

                // 每完成一根K线输出一次状态（可选，避免日志过多）
                if (this.completedBars.Count % 5 == 0) // 每5根K线输出一次状态
                {
                    this.LogStrategyMetrics();
                }

                // 重置当前K线数据
                this.ResetCurrentBar();
            }
            catch (Exception ex)
            {
                this.Log($"完成K线时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 重置当前K线数据
        /// </summary>
        private void ResetCurrentBar()
        {
            this.currentTickCount = 0;
            this.currentDelta = 0;
            this.currentOpen = this.currentClose; // 下一根K线的开盘价为当前收盘价
            this.currentBarSignalChecked = false; // 重置信号检查标志
        }

        #endregion

        #region 交易信号检测

        /// <summary>
        /// 检查交易信号
        /// </summary>
        private void CheckTradingSignals()
        {
            try
            {
                // 需要至少2根完成的K线才能进行分析
                if (this.completedBars.Count < 2)
                    return;

                // 如果当前K线已经检查过信号，跳过（每根K线只检查一次）
                if (this.currentBarSignalChecked)
                    return;

                // 如果正在等待开仓或平仓，跳过信号检测
                if (this.waitOpenPosition || this.waitClosePositions)
                    return;

                // 获取最近两根K线
                var previousBar = this.completedBars[this.completedBars.Count - 2];
                var currentBar = this.completedBars[this.completedBars.Count - 1];

                // 获取当前持仓
                var positions = Core.Instance.Positions.Where(x =>
                    x.Symbol == this.CurrentSymbol &&
                    x.Account == this.CurrentAccount).ToArray();

                // 如果有持仓，检查平仓信号
                if (positions.Any())
                {
                    this.CheckExitSignals(positions, previousBar, currentBar);
                }
                else
                {
                    // 如果没有持仓，检查开仓信号
                    this.CheckEntrySignals(previousBar, currentBar);
                }

                // 标记当前K线已检查过信号
                this.currentBarSignalChecked = true;
            }
            catch (Exception ex)
            {
                this.Log($"检查交易信号时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 检查开仓信号
        /// </summary>
        private void CheckEntrySignals(TickBarData previousBar, TickBarData currentBar)
        {
            // 做多信号：连续两根K线收盘价>开盘价，且Delta值都>阈值
            if (previousBar.IsBullish && currentBar.IsBullish &&
                previousBar.Delta > this.DeltaThreshold && currentBar.Delta > this.DeltaThreshold)
            {
                this.OpenLongPosition();
                return;
            }

            // 做空信号：连续两根K线收盘价<开盘价，且Delta值都<-阈值
            if (previousBar.IsBearish && currentBar.IsBearish &&
                previousBar.Delta < -this.DeltaThreshold && currentBar.Delta < -this.DeltaThreshold)
            {
                this.OpenShortPosition();
                return;
            }
        }

        /// <summary>
        /// 检查平仓信号（这里可以添加更复杂的平仓逻辑）
        /// </summary>
        private void CheckExitSignals(Position[] positions, TickBarData previousBar, TickBarData currentBar)
        {
            // 当前使用止盈止损自动平仓，这里可以添加其他平仓条件
            // 例如：反向信号平仓等
        }

        #endregion

        #region 交易操作

        /// <summary>
        /// 开多仓
        /// </summary>
        private void OpenLongPosition()
        {
            try
            {
                this.waitOpenPosition = true;
                this.Log("检测到做多信号，准备开多仓", StrategyLoggingLevel.Trading);

                // 计算止盈止损价格 - 正确：将点数转换为价格差值
                double currentPrice = this.CurrentSymbol.Ask;
                double takeProfitPriceDiff = this.TakeProfit * MNQ_TICK_SIZE;  // 点数转换为价格差值
                double stopLossPriceDiff = this.StopLoss * MNQ_TICK_SIZE;      // 点数转换为价格差值
                double takeProfitPrice = currentPrice + takeProfitPriceDiff;   // 做多：加上止盈差值
                double stopLossPrice = currentPrice - stopLossPriceDiff;       // 做多：减去止损差值

                this.Log($"[DEBUG] 做多价格计算 - 买入价: {currentPrice:F4}, 止盈点数: {this.TakeProfit:F1}点(+{takeProfitPriceDiff:F4}), 止损点数: {this.StopLoss:F1}点(-{stopLossPriceDiff:F4})", StrategyLoggingLevel.Info);
                this.Log($"[DEBUG] 计算结果 - 止盈价格: {takeProfitPrice:F4}, 止损价格: {stopLossPrice:F4}", StrategyLoggingLevel.Info);

                // 临时测试：先不设置止盈止损，只开仓
                var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
                {
                    Account = this.CurrentAccount,
                    Symbol = this.CurrentSymbol,
                    OrderTypeId = this.orderTypeId,
                    Quantity = this.Quantity,
                    Side = Side.Buy
                    // TakeProfit = SlTpHolder.CreateTP(takeProfitPrice),
                    // StopLoss = SlTpHolder.CreateSL(stopLossPrice)
                });

                if (result.Status == TradingOperationResultStatus.Failure)
                {
                    this.Log($"开多仓失败: {result.Message ?? result.Status.ToString()}", StrategyLoggingLevel.Trading);
                    this.ProcessTradingRefuse();
                }
                else
                {
                    this.Log($"开多仓成功 - 价格: {currentPrice:F4}, 止盈: {takeProfitPrice:F4}, 止损: {stopLossPrice:F4}", StrategyLoggingLevel.Trading);
                }
            }
            catch (Exception ex)
            {
                this.Log($"开多仓时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
                this.ProcessTradingRefuse();
            }
        }

        /// <summary>
        /// 开空仓
        /// </summary>
        private void OpenShortPosition()
        {
            try
            {
                this.waitOpenPosition = true;
                this.Log("检测到做空信号，准备开空仓", StrategyLoggingLevel.Trading);

                // 计算止盈止损价格 - 正确：将点数转换为价格差值
                double currentPrice = this.CurrentSymbol.Bid;
                double takeProfitPriceDiff = this.TakeProfit * MNQ_TICK_SIZE;  // 点数转换为价格差值
                double stopLossPriceDiff = this.StopLoss * MNQ_TICK_SIZE;      // 点数转换为价格差值
                double takeProfitPrice = currentPrice - takeProfitPriceDiff;   // 做空：减去止盈差值
                double stopLossPrice = currentPrice + stopLossPriceDiff;       // 做空：加上止损差值

                this.Log($"[DEBUG] 做空价格计算 - 卖出价: {currentPrice:F4}, 止盈点数: {this.TakeProfit:F1}点(-{takeProfitPriceDiff:F4}), 止损点数: {this.StopLoss:F1}点(+{stopLossPriceDiff:F4})", StrategyLoggingLevel.Info);
                this.Log($"[DEBUG] 计算结果 - 止盈价格: {takeProfitPrice:F4}, 止损价格: {stopLossPrice:F4}", StrategyLoggingLevel.Info);

                // 临时测试：先不设置止盈止损，只开仓
                var result = Core.Instance.PlaceOrder(new PlaceOrderRequestParameters()
                {
                    Account = this.CurrentAccount,
                    Symbol = this.CurrentSymbol,
                    OrderTypeId = this.orderTypeId,
                    Quantity = this.Quantity,
                    Side = Side.Sell
                    // TakeProfit = SlTpHolder.CreateTP(takeProfitPrice),
                    // StopLoss = SlTpHolder.CreateSL(stopLossPrice)
                });

                if (result.Status == TradingOperationResultStatus.Failure)
                {
                    this.Log($"开空仓失败: {result.Message ?? result.Status.ToString()}", StrategyLoggingLevel.Trading);
                    this.ProcessTradingRefuse();
                }
                else
                {
                    this.Log($"开空仓成功 - 价格: {currentPrice:F4}, 止盈: {takeProfitPrice:F4}, 止损: {stopLossPrice:F4}", StrategyLoggingLevel.Trading);
                }
            }
            catch (Exception ex)
            {
                this.Log($"开空仓时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
                this.ProcessTradingRefuse();
            }
        }

        /// <summary>
        /// 处理交易拒绝
        /// </summary>
        private void ProcessTradingRefuse()
        {
            this.waitOpenPosition = false;
            this.waitClosePositions = false;
            this.Log("交易操作被拒绝，重置等待标志", StrategyLoggingLevel.Error);
        }

        /// <summary>
        /// 为持仓设置止盈止损订单
        /// </summary>
        private void SetStopLossAndTakeProfit(Position position)
        {
            try
            {
                // 计算止盈止损价格
                double takeProfitPriceDiff = this.TakeProfit * MNQ_TICK_SIZE;
                double stopLossPriceDiff = this.StopLoss * MNQ_TICK_SIZE;

                double takeProfitPrice, stopLossPrice;

                if (position.Side == Side.Buy)
                {
                    // 做多：止盈在上方，止损在下方
                    takeProfitPrice = position.OpenPrice + takeProfitPriceDiff;
                    stopLossPrice = position.OpenPrice - stopLossPriceDiff;
                }
                else
                {
                    // 做空：止盈在下方，止损在上方
                    takeProfitPrice = position.OpenPrice - takeProfitPriceDiff;
                    stopLossPrice = position.OpenPrice + stopLossPriceDiff;
                }

                this.Log($"[DEBUG] 为持仓设置止盈止损 - 开仓价: {position.OpenPrice:F4}, 止盈: {takeProfitPrice:F4}, 止损: {stopLossPrice:F4}", StrategyLoggingLevel.Info);

                // 设置止损订单
                this.PlaceStopLossOrder(position, stopLossPrice);

                // 设置止盈订单
                this.PlaceTakeProfitOrder(position, takeProfitPrice);
            }
            catch (Exception ex)
            {
                this.Log($"设置止盈止损时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 下止损订单
        /// </summary>
        private void PlaceStopLossOrder(Position position, double stopLossPrice)
        {
            try
            {
                var stopOrderType = this.CurrentSymbol.GetAlowedOrderTypes(OrderTypeUsage.CloseOrder)
                    .FirstOrDefault(ot => ot.Behavior == OrderTypeBehavior.Stop);

                if (stopOrderType == null)
                {
                    this.Log("无法找到止损订单类型", StrategyLoggingLevel.Error);
                    return;
                }

                var request = new PlaceOrderRequestParameters()
                {
                    Account = this.CurrentAccount,
                    Symbol = this.CurrentSymbol,
                    OrderTypeId = stopOrderType.Id,
                    Quantity = position.Quantity,
                    Side = position.Side == Side.Buy ? Side.Sell : Side.Buy, // 反向平仓
                    TriggerPrice = stopLossPrice
                };

                var result = Core.Instance.PlaceOrder(request);

                if (result.Status == TradingOperationResultStatus.Failure)
                {
                    this.Log($"止损订单下单失败: {result.Message}", StrategyLoggingLevel.Error);
                }
                else
                {
                    this.currentStopLossOrderId = result.OrderId; // 保存订单ID
                    this.Log($"止损订单下单成功 - 触发价格: {stopLossPrice:F4}, 订单ID: {result.OrderId}", StrategyLoggingLevel.Trading);
                }
            }
            catch (Exception ex)
            {
                this.Log($"下止损订单时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 下止盈订单
        /// </summary>
        private void PlaceTakeProfitOrder(Position position, double takeProfitPrice)
        {
            try
            {
                var limitOrderType = this.CurrentSymbol.GetAlowedOrderTypes(OrderTypeUsage.CloseOrder)
                    .FirstOrDefault(ot => ot.Behavior == OrderTypeBehavior.Limit);

                if (limitOrderType == null)
                {
                    this.Log("无法找到止盈订单类型", StrategyLoggingLevel.Error);
                    return;
                }

                var request = new PlaceOrderRequestParameters()
                {
                    Account = this.CurrentAccount,
                    Symbol = this.CurrentSymbol,
                    OrderTypeId = limitOrderType.Id,
                    Quantity = position.Quantity,
                    Side = position.Side == Side.Buy ? Side.Sell : Side.Buy, // 反向平仓
                    Price = takeProfitPrice
                };

                var result = Core.Instance.PlaceOrder(request);

                if (result.Status == TradingOperationResultStatus.Failure)
                {
                    this.Log($"止盈订单下单失败: {result.Message}", StrategyLoggingLevel.Error);
                }
                else
                {
                    this.currentTakeProfitOrderId = result.OrderId; // 保存订单ID
                    this.Log($"止盈订单下单成功 - 限价: {takeProfitPrice:F4}, 订单ID: {result.OrderId}", StrategyLoggingLevel.Trading);
                }
            }
            catch (Exception ex)
            {
                this.Log($"下止盈订单时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 取消挂起的止盈止损订单
        /// </summary>
        private void CancelPendingOrders()
        {
            try
            {
                // 取消止盈订单
                if (!string.IsNullOrEmpty(this.currentTakeProfitOrderId))
                {
                    var cancelResult = Core.Instance.CancelOrder(this.currentTakeProfitOrderId, this.CurrentAccount);
                    if (cancelResult.Status == TradingOperationResultStatus.Success)
                    {
                        this.Log($"已取消止盈订单: {this.currentTakeProfitOrderId}", StrategyLoggingLevel.Trading);
                    }
                    else
                    {
                        this.Log($"取消止盈订单失败: {cancelResult.Message ?? "未知错误"}", StrategyLoggingLevel.Error);
                    }
                    this.currentTakeProfitOrderId = null;
                }

                // 取消止损订单
                if (!string.IsNullOrEmpty(this.currentStopLossOrderId))
                {
                    var cancelResult = Core.Instance.CancelOrder(this.currentStopLossOrderId, this.CurrentAccount);
                    if (cancelResult.Status == TradingOperationResultStatus.Success)
                    {
                        this.Log($"已取消止损订单: {this.currentStopLossOrderId}", StrategyLoggingLevel.Trading);
                    }
                    else
                    {
                        this.Log($"取消止损订单失败: {cancelResult.Message ?? "未知错误"}", StrategyLoggingLevel.Error);
                    }
                    this.currentStopLossOrderId = null;
                }
            }
            catch (Exception ex)
            {
                this.Log($"取消挂起订单时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 持仓添加事件
        /// </summary>
        private void Core_PositionAdded(Position obj)
        {
            if (obj.Symbol == this.CurrentSymbol && obj.Account == this.CurrentAccount)
            {
                this.waitOpenPosition = false;
                // 修复：Position属性简化处理，使用当前市价
                double positionPrice = this.CurrentSymbol.Last;
                this.Log($"持仓已开启 - 方向: {obj.Side}, 数量: {obj.Quantity}, 价格: {positionPrice:F4}", StrategyLoggingLevel.Trading);

                // 持仓开启后，设置止盈止损
                this.SetStopLossAndTakeProfit(obj);
            }
        }

        /// <summary>
        /// 持仓移除事件
        /// </summary>
        private void Core_PositionRemoved(Position obj)
        {
            if (obj.Symbol == this.CurrentSymbol && obj.Account == this.CurrentAccount)
            {
                this.waitClosePositions = false;
                // 修复：Position属性简化处理，不显示具体盈亏
                this.Log($"持仓已平仓 - 方向: {obj.Side}, 数量: {obj.Quantity}", StrategyLoggingLevel.Trading);

                // 持仓平仓后，取消对应的止盈止损订单
                this.CancelPendingOrders();
            }
        }

        /// <summary>
        /// 订单历史添加事件
        /// </summary>
        private void Core_OrdersHistoryAdded(OrderHistory obj)
        {
            if (obj.Symbol != this.CurrentSymbol || obj.Account != this.CurrentAccount)
                return;

            if (obj.Status == OrderStatus.Refused)
            {
                // .NET 8.0: 使用现代化的模式匹配和字符串插值
                var reason = obj.Status switch
                {
                    OrderStatus.Refused => "订单被拒绝",
                    OrderStatus.Cancelled => "订单被取消",
                    _ => obj.Status.ToString()
                };
                this.Log($"订单状态: {reason}", StrategyLoggingLevel.Error);
                this.ProcessTradingRefuse();
            }
        }

        /// <summary>
        /// 交易添加事件
        /// </summary>
        private void Core_TradeAdded(Trade obj)
        {
            if (obj.Symbol != this.CurrentSymbol || obj.Account != this.CurrentAccount)
                return;

            // 简化处理：记录交易完成信息
            try
            {
                this.Log($"交易完成 - 品种: {obj.Symbol?.Name}, 数量: {obj.Quantity}, 价格: {obj.Price:F4}", StrategyLoggingLevel.Trading);
            }
            catch
            {
                this.Log("交易完成", StrategyLoggingLevel.Trading);
            }
        }

        #endregion

        #region 指标显示

        /// <summary>
        /// 初始化策略指标（.NET 8.0现代化版本）
        /// </summary>
        protected override void OnInitializeMetrics(Meter meter)
        {
            base.OnInitializeMetrics(meter);

            // 创建可观察的计数器和仪表
            // 修复：使用更频繁更新的指标
            meter.CreateObservableGauge("current-tick-count", () => this.currentTickCount, description: "当前Tick计数");
            meter.CreateObservableGauge("tick-total-count", () => this.TickCount, description: "Tick总数");
            meter.CreateObservableGauge("current-delta", () => this.currentDelta, description: "当前Delta值");

            meter.CreateObservableGauge("last-bar-delta", () =>
                this.completedBars.Count > 0 ? this.completedBars[^1].Delta : 0, description: "最新K线Delta");

            meter.CreateObservableCounter("total-net-pnl", () => this.totalNetPl, description: "总净盈亏");
            meter.CreateObservableCounter("total-fee", () => this.totalFee, description: "总手续费");

            // 持仓数量
            meter.CreateObservableGauge("position-count", () =>
            {
                var positions = Core.Instance.Positions.Where(x =>
                    x.Symbol == this.CurrentSymbol &&
                    x.Account == this.CurrentAccount).ToArray();
                return positions.Length;
            }, description: "当前持仓数量");
        }

        /// <summary>
        /// 策略运行时状态监控（通过日志输出）
        /// </summary>
        private void LogStrategyMetrics()
        {
            try
            {
                var status = this.GetStrategyStatus();
                this.Log($"策略状态更新:\n{status}", StrategyLoggingLevel.Info);
            }
            catch (Exception ex)
            {
                this.Log($"获取策略状态时发生错误: {ex.Message}", StrategyLoggingLevel.Error);
            }
        }

        /// <summary>
        /// 获取策略状态信息（用于日志显示）
        /// </summary>
        private string GetStrategyStatus()
        {
            var status = new System.Text.StringBuilder();

            status.AppendLine($"Tick进度: {this.currentTickCount}/{this.TickCount}");
            status.AppendLine($"当前Delta: {this.currentDelta:F2}");

            if (this.completedBars.Count > 0)
            {
                var lastBar = this.completedBars.Last();
                status.AppendLine($"最新K线Delta: {lastBar.Delta:F2}");
                status.AppendLine($"K线类型: {(lastBar.IsBullish ? "阳线" : (lastBar.IsBearish ? "阴线" : "十字"))}");
            }

            status.AppendLine($"总净盈亏: {this.totalNetPl:F2}");
            status.AppendLine($"总手续费: {this.totalFee:F2}");

            var positions = Core.Instance.Positions.Where(x =>
                x.Symbol == this.CurrentSymbol &&
                x.Account == this.CurrentAccount).ToArray();

            status.AppendLine($"当前持仓: {(positions.Length > 0 ? $"{positions[0].Side} {positions[0].Quantity}" : "无持仓")}");
            status.AppendLine($"等待状态: {(this.waitOpenPosition ? "等待开仓" : (this.waitClosePositions ? "等待平仓" : "空闲"))}");

            return status.ToString();
        }

        #endregion
    }
}
